//
//  HomeView.swift
//  ztt2
//
//  Created by AI Assistant on 2025/7/29.
//

import SwiftUI

/**
 * 首页视图 - 家庭成员管理
 */
struct HomeView: View {

    // MARK: - Properties
    let onMemberSelected: (String) -> Void

    @StateObject private var viewModel = HomeViewModel()
    @StateObject private var syncManager = iCloudSyncManager.shared
    @State private var pageAppeared = false
    @State private var logoRotation: Double = 0
    @State private var isDeleteMode = false
    @State private var showAddMemberForm = false
    @State private var showFamilyOperationOptions = false
    @State private var showFamilyOperationForm = false
    @State private var showFamilyTotalScore = false
    @State private var showDateRangePicker = false
    @State private var showLotteryConfigDialog = false
    @State private var lotteryConfigPressed = false
    @State private var showMemberSelectionPopup = false
    @State private var showLotteryToolSelectionPopup = false
    @State private var selectedMemberForLottery: Member?
    @State private var familyOperationType: FamilyOperationType = .add
    @State private var selectedDateRange: DateRangeType = .thisMonth

    // 抽奖道具配置弹窗状态
    @State private var showWheelConfigPopup = false
    @State private var showBlindBoxConfigPopup = false
    @State private var showScratchCardConfigPopup = false

    // 提示消息状态
    @State private var showSuccessAlert = false
    @State private var showErrorAlert = false
    @State private var alertMessage = ""

    // 计算属性：将Core Data的Member转换为FamilyMemberGridView需要的格式
    private var gridMembers: [FamilyMemberGridView.FamilyMember] {
        viewModel.members.map { member in
            FamilyMemberGridView.FamilyMember(
                id: member.objectID.uriRepresentation().absoluteString,
                name: member.displayName,
                role: member.role ?? "other",
                currentPoints: Int(member.currentPoints)
            )
        }
    }

    private var totalScore: Int {
        viewModel.getTotalScoreForDateRange(selectedDateRange)
    }

    var body: some View {
        ZStack {
            // 美化背景渐变
            LinearGradient(
                gradient: Gradient(stops: [
                    .init(color: Color(hex: "#fcfff4"), location: 0.0),
                    .init(color: Color(hex: "#f8fdf0"), location: 0.3),
                    .init(color: Color.white, location: 0.7),
                    .init(color: Color(hex: "#fafffe"), location: 1.0)
                ]),
                startPoint: .top,
                endPoint: .bottom
            )
            .ignoresSafeArea(.all)

            // 装饰性背景元素
            VStack {
                HStack {
                    Spacer()
                    Circle()
                        .fill(Color(hex: "#B5E36B").opacity(0.03))
                        .frame(width: 120, height: 120)
                        .offset(x: 40, y: -20)
                }
                Spacer()
                HStack {
                    Circle()
                        .fill(Color(hex: "#FFE49E").opacity(0.04))
                        .frame(width: 80, height: 80)
                        .offset(x: -30, y: 50)
                    Spacer()
                }
            }

            VStack(spacing: 0) {
                // 同步状态指示器
                if syncManager.syncStatus == .syncing {
                    HStack {
                        Image(systemName: "icloud.and.arrow.down")
                            .foregroundColor(.blue)
                            .font(.caption)
                        Text("正在同步数据...")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    .padding(.horizontal)
                    .padding(.vertical, 4)
                    .background(Color.blue.opacity(0.1))
                    .cornerRadius(8)
                    .padding(.top, 8)
                    .transition(.opacity)
                }

                // 顶部区域 - Logo和配置按钮
                HStack {
                    // Logo with enhanced visual effects
                    ZStack {
                        // 背景渐变圆形
                        Circle()
                            .fill(
                                LinearGradient(
                                    gradient: Gradient(colors: [
                                        Color(hex: "#FFE49E").opacity(0.3),
                                        Color(hex: "#B5E36B").opacity(0.2)
                                    ]),
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                )
                            )
                            .frame(width: 110, height: 110)
                            .shadow(color: Color(hex: "#B5E36B").opacity(0.3), radius: 15, x: 0, y: 5)

                        // Logo图片
                        Image("logo")
                            .resizable()
                            .aspectRatio(contentMode: .fit)
                            .frame(width: 90, height: 90)
                            .rotationEffect(.degrees(logoRotation))
                            .onTapGesture {
                                withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
                                    logoRotation += 360
                                }
                            }
                    }
                    .offset(y: -20)

                    Spacer()

                    // 抽奖道具配置按钮
                    Button(action: {
                        withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                            lotteryConfigPressed = true
                        }

                        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                            lotteryConfigPressed = false
                            handleLotteryConfig()
                        }
                    }) {
                        ZStack {
                            // 美化背景容器
                            RoundedRectangle(cornerRadius: 16)
                                .fill(
                                    LinearGradient(
                                        gradient: Gradient(colors: [
                                            Color(hex: "#f8ffe5"),
                                            Color(hex: "#edf6d9")
                                        ]),
                                        startPoint: .topLeading,
                                        endPoint: .bottomTrailing
                                    )
                                )
                                .frame(width: 120, height: 44)
                                .shadow(color: Color(hex: "#a9d051").opacity(0.25), radius: lotteryConfigPressed ? 10 : 6, x: 0, y: lotteryConfigPressed ? 4 : 2)
                                .overlay(
                                    RoundedRectangle(cornerRadius: 16)
                                        .stroke(Color(hex: "#a9d051").opacity(0.2), lineWidth: 1)
                                )

                            // 装饰圆点
                            HStack {
                                Spacer()
                                VStack {
                                    Circle()
                                        .fill(Color(hex: "#a9d051").opacity(0.15))
                                        .frame(width: 20, height: 20)
                                        .offset(x: 8, y: -8)
                                    Spacer()
                                }
                            }

                            // 内容
                            HStack(spacing: 6) {
                                // 图标
                                Image("shezhi")
                                    .resizable()
                                    .aspectRatio(contentMode: .fit)
                                    .frame(width: 20, height: 20)
                                    .foregroundColor(Color(hex: "#a9d051"))

                                // 文字
                                Text("抽奖配置")
                                    .font(.system(size: 14, weight: .semibold))
                                    .foregroundColor(Color(hex: "#a9d051"))
                                    .lineLimit(1)
                                    .minimumScaleFactor(0.8)
                            }

                            // 按压闪烁效果
                            if lotteryConfigPressed {
                                RoundedRectangle(cornerRadius: 16)
                                    .fill(Color.white.opacity(0.4))
                                    .frame(width: 120, height: 44)
                                    .transition(.opacity)
                            }
                        }
                        .frame(width: 120, height: 44)
                        .scaleEffect(lotteryConfigPressed ? 0.95 : 1.0)
                    }
                    .buttonStyle(PlainButtonStyle())
                    .offset(y: -20)
                }
                .padding(.horizontal, DesignSystem.Spacing.pageHorizontal)
                .padding(.top, 5)
                .opacity(pageAppeared ? 1.0 : 0.0)
                .offset(y: pageAppeared ? 0 : -50)
                .animation(Animation.spring(response: 0.8, dampingFraction: 0.8).delay(0.1), value: pageAppeared)

                Spacer()
                    .frame(height: 5)

                // 操作按钮区域
                ActionButtonsView(
                    totalScore: totalScore,
                    dateRangeText: selectedDateRange.displayText,
                    onAddMemberTapped: {
                        handleAddMember()
                    },
                    onFamilyOperationTapped: {
                        handleFamilyOperation()
                    },
                    onTotalScoreTapped: {
                        handleTotalScoreTapped()
                    }
                )
                .opacity(pageAppeared ? 1.0 : 0.0)
                .offset(y: pageAppeared ? 0 : 30)
                .animation(Animation.spring(response: 0.8, dampingFraction: 0.8).delay(0.3), value: pageAppeared)

                Spacer()
                    .frame(height: 20)

                // 家庭成员网格视图
                FamilyMemberGridView(
                    members: gridMembers,
                    isDeleteMode: isDeleteMode,
                    onMemberTapped: { member in
                        handleMemberTapped(member)
                    },
                    onEnterDeleteMode: {
                        enterDeleteMode()
                    },
                    onExitDeleteMode: {
                        exitDeleteMode()
                    },
                    onDeleteRequested: { member in
                        requestDeleteMember(member)
                    },
                    onRefresh: {
                        await refreshData()
                    }
                )
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .opacity(pageAppeared ? 1.0 : 0.0)
                .offset(y: pageAppeared ? 0 : 30)
                .animation(Animation.spring(response: 0.8, dampingFraction: 0.8).delay(0.7), value: pageAppeared)
            }
        }
        .onTapGesture {
            // 点击空白区域退出删除模式
            if isDeleteMode {
                exitDeleteMode()
            }
        }
        .onAppear {
            // Logo入场动画
            withAnimation(.spring(response: 0.8, dampingFraction: 0.6).delay(0.2)) {
                logoRotation = 0
            }

            // 页面入场动画
            withAnimation {
                pageAppeared = true
            }
        }
        .overlay(
            // 添加成员表单弹窗
            AddMemberFormView(
                isPresented: $showAddMemberForm,
                onSubmit: { memberData in
                    handleAddMemberSubmit(memberData)
                },
                onCancel: {
                    showAddMemberForm = false
                }
            )
        )
        .overlay(
            // 全员操作选项弹窗
            FamilyOperationOptionsView(
                isPresented: $showFamilyOperationOptions,
                onAddPoints: {
                    familyOperationType = .add
                    showFamilyOperationForm = true
                },
                onDeductPoints: {
                    familyOperationType = .deduct
                    showFamilyOperationForm = true
                }
            )
        )
        .overlay(
            // 全员操作表单弹窗
            FamilyOperationFormView(
                isPresented: $showFamilyOperationForm,
                operationType: familyOperationType,
                onSubmit: { name, value in
                    handleFamilyOperationSubmit(name: name, value: value, type: familyOperationType)
                },
                onCancel: {
                    showFamilyOperationForm = false
                }
            )
        )
        .overlay(
            // 日期范围选择器弹窗
            Group {
                if showDateRangePicker {
                    DateRangePickerView(
                        selectedDateRange: $selectedDateRange,
                        isPresented: $showDateRangePicker
                    )
                    .transition(.opacity)
                }
            }
        )
        // 成员选择弹窗
        .overlay(
            MemberSelectionPopupView(
                isPresented: $showMemberSelectionPopup,
                members: viewModel.members,
                onMemberSelected: { member in
                    selectedMemberForLottery = member
                    showMemberSelectionPopup = false
                    showLotteryToolSelectionPopup = true
                }
            )
        )
        // 抽奖道具选择弹窗
        .overlay(
            LotteryToolSelectionPopupView(
                isPresented: $showLotteryToolSelectionPopup,
                selectedMember: selectedMemberForLottery,
                onToolSelected: { member, toolType in
                    showLotteryToolSelectionPopup = false
                    handleLotteryToolSelected(member: member, toolType: toolType)
                }
            )
        )
        // 大转盘配置弹窗
        .overlay(
            WheelConfigPopupView(
                isPresented: $showWheelConfigPopup,
                selectedMember: selectedMemberForLottery,
                onSave: { configData in
                    handleWheelConfigSave(configData)
                },
                onCancel: {
                    showWheelConfigPopup = false
                }
            )
        )
        // 盲盒配置弹窗
        .overlay(
            BlindBoxConfigPopupView(
                isPresented: $showBlindBoxConfigPopup,
                selectedMember: selectedMemberForLottery,
                onSave: { configData in
                    handleBlindBoxConfigSave(configData)
                },
                onCancel: {
                    showBlindBoxConfigPopup = false
                }
            )
        )
        // 刮刮卡配置弹窗
        .overlay(
            ScratchCardConfigPopupView(
                isPresented: $showScratchCardConfigPopup,
                selectedMember: selectedMemberForLottery,
                onSave: { configData in
                    handleScratchCardConfigSave(configData)
                },
                onCancel: {
                    showScratchCardConfigPopup = false
                }
            )
        )
        // 成功提示Alert
        .alert("提示", isPresented: $showSuccessAlert) {
            Button("确定") { }
        } message: {
            Text(alertMessage)
        }
        // 错误提示Alert
        .alert("错误", isPresented: $showErrorAlert) {
            Button("确定") { }
        } message: {
            Text(alertMessage)
        }
    }

    // MARK: - Action Handlers

    /**
     * 处理添加成员按钮点击
     */
    private func handleAddMember() {
        withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
            showAddMemberForm = true
        }
    }

    /**
     * 处理全员操作按钮点击
     */
    private func handleFamilyOperation() {
        withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
            showFamilyOperationOptions = true
        }
    }

    /**
     * 处理抽奖道具配置按钮点击
     */
    private func handleLotteryConfig() {
        withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
            showMemberSelectionPopup = true
        }
    }

    /**
     * 处理全家总分按钮点击
     */
    private func handleTotalScoreTapped() {
        print("显示时间范围选择弹窗")
        withAnimation(.spring(response: 0.3, dampingFraction: 0.8)) {
            showDateRangePicker = true
        }
    }

    /**
     * 处理添加成员表单提交
     */
    private func handleAddMemberSubmit(_ memberData: MemberFormData) {
        print("添加成员: \(memberData.name), 初始积分: \(memberData.initialPointsValue), 角色: \(memberData.role)")

        // 调用ViewModel添加成员
        viewModel.addMember(
            name: memberData.name,
            role: memberData.role,
            birthDate: memberData.birthDate,
            initialPoints: memberData.initialPointsValue
        )

        showAddMemberForm = false
    }

    /**
     * 处理全员操作表单提交
     */
    private func handleFamilyOperationSubmit(name: String, value: Int, type: FamilyOperationType) {
        let operationText = type == .add ? "全家加分" : "全家扣分"
        print("\(operationText): \(name), 分值: \(value)")

        // 调用ViewModel进行全员操作
        if type == .add {
            viewModel.addPointsToAllMembers(reason: name, value: value)
        } else {
            viewModel.deductPointsFromAllMembers(reason: name, value: value)
        }

        showFamilyOperationForm = false
    }

    /**
     * 处理家庭成员卡片点击
     */
    private func handleMemberTapped(_ member: FamilyMemberGridView.FamilyMember) {
        print("点击了家庭成员: \(member.name)")
        withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
            // 添加点击反馈动画
        }

        // 调用导航回调，传递成员ID
        onMemberSelected(member.id)
    }

    /**
     * 进入删除模式
     */
    private func enterDeleteMode() {
        withAnimation(.spring(response: 0.3, dampingFraction: 0.8)) {
            isDeleteMode = true
        }
    }

    /**
     * 退出删除模式
     */
    private func exitDeleteMode() {
        withAnimation(.spring(response: 0.3, dampingFraction: 0.8)) {
            isDeleteMode = false
        }
    }

    /**
     * 请求删除成员
     */
    private func requestDeleteMember(_ member: FamilyMemberGridView.FamilyMember) {
        print("请求删除成员: \(member.name)")

        // 根据ID找到对应的Core Data Member对象
        if let coreDataMember = viewModel.members.first(where: {
            $0.objectID.uriRepresentation().absoluteString == member.id
        }) {
            // 调用ViewModel删除成员
            viewModel.deleteMember(coreDataMember)
        }

        // 退出删除模式
        exitDeleteMode()
    }

    // MARK: - 抽奖配置处理方法

    /**
     * 处理抽奖道具选择
     */
    private func handleLotteryToolSelected(member: Member, toolType: LotteryConfig.ToolType) {
        selectedMemberForLottery = member

        withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
            switch toolType {
            case .wheel:
                showWheelConfigPopup = true
            case .blindbox:
                showBlindBoxConfigPopup = true
            case .scratchcard:
                showScratchCardConfigPopup = true
            }
        }

        print("为成员 \(member.name ?? "未知") 配置 \(toolType.displayName)")
    }

    /**
     * 处理大转盘配置保存
     */
    private func handleWheelConfigSave(_ configData: WheelConfigData) {
        guard let member = selectedMemberForLottery else {
            print("❌ 未选择成员，无法保存大转盘配置")
            return
        }

        print("开始保存大转盘配置: 成员=\(member.name ?? "未知"), 分区数=\(configData.sectorCount), 积分=\(configData.costPerPlay)")
        print("奖品列表: \(configData.sectorPrizes)")

        // 使用DataManager保存配置
        let savedConfig = DataManager.shared.saveWheelConfig(
            for: member,
            sectorCount: configData.sectorCount,
            costPerPlay: configData.costPerPlay,
            sectorPrizes: configData.sectorPrizes
        )

        // 关闭弹窗
        showWheelConfigPopup = false

        // 根据保存结果显示提示
        if savedConfig != nil {
            // 保存成功
            showSuccessMessage("lottery_config.save.success".localized)
            print("✅ 大转盘配置保存成功")
        } else {
            // 保存失败
            showErrorMessage("lottery_config.save.error".localized)
            print("❌ 大转盘配置保存失败")
        }
    }

    /**
     * 处理盲盒配置保存
     */
    private func handleBlindBoxConfigSave(_ configData: BlindBoxConfigData) {
        guard let member = selectedMemberForLottery else { return }

        print("保存盲盒配置: 成员=\(member.name ?? "未知"), 盲盒数=\(configData.boxCount), 积分=\(configData.costPerPlay)")
        print("奖品列表: \(configData.boxPrizes)")

        // 调用DataManager保存盲盒配置
        let savedConfig = DataManager.shared.saveBlindBoxConfig(
            for: member,
            boxCount: configData.boxCount,
            costPerPlay: configData.costPerPlay,
            boxPrizes: configData.boxPrizes
        )

        // 关闭弹窗
        showBlindBoxConfigPopup = false

        // 根据保存结果显示提示
        if savedConfig != nil {
            // 保存成功
            showSuccessMessage("lottery_config.save.success".localized)
            print("✅ 盲盒配置保存成功")
        } else {
            // 保存失败
            showErrorMessage("lottery_config.save.error".localized)
            print("❌ 盲盒配置保存失败")
        }
    }

    /**
     * 处理刮刮卡配置保存
     */
    private func handleScratchCardConfigSave(_ configData: ScratchCardConfigData) {
        guard let member = selectedMemberForLottery else {
            showErrorMessage("lottery_config.error.no_member_selected".localized)
            return
        }

        print("保存刮刮卡配置: 成员=\(member.name ?? "未知"), 刮刮卡数=\(configData.cardCount), 积分=\(configData.costPerPlay)")
        print("奖品列表: \(configData.cardPrizes)")

        // 使用DataManager保存刮刮卡配置
        if let savedConfig = DataManager.shared.saveScratchCardConfig(
            for: member,
            cardCount: configData.cardCount,
            costPerPlay: configData.costPerPlay,
            cardPrizes: configData.cardPrizes
        ) {
            // 保存成功
            showScratchCardConfigPopup = false

            // 显示成功提示
            let successMessage = String(format: "lottery_config.scratchcard.save_success".localized,
                                      member.displayName,
                                      configData.cardCount,
                                      configData.costPerPlay)
            showSuccessMessage(successMessage)

            print("✅ 刮刮卡配置保存成功: ID=\(savedConfig.id?.uuidString ?? "未知")")
        } else {
            // 保存失败
            showErrorMessage("lottery_config.scratchcard.save_failed".localized)
            print("❌ 刮刮卡配置保存失败")
        }
    }



    /**
     * 刷新数据
     */
    private func refreshData() async {
        print("下拉刷新数据")
        await MainActor.run {
            viewModel.refresh()
        }
    }

    // MARK: - 提示消息方法

    /**
     * 显示成功提示
     */
    private func showSuccessMessage(_ message: String) {
        alertMessage = message
        showSuccessAlert = true
    }

    /**
     * 显示错误提示
     */
    private func showErrorMessage(_ message: String) {
        alertMessage = message
        showErrorAlert = true
    }
}

// MARK: - Preview
#Preview {
    HomeView { memberId in
        print("选中成员: \(memberId)")
    }
}
